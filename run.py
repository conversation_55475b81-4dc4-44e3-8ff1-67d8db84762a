#!/usr/bin/env python3
import os
from flask_migrate import upgrade
from app import create_app, db
from app.models import User

app = create_app(os.getenv('FLASK_CONFIG') or 'default')

@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User}

@app.cli.command()
def init_db():
    """初始化数据库"""
    db.create_all()
    print('数据库初始化完成！')

@app.cli.command()
def create_admin():
    """创建管理员用户"""
    username = input('请输入管理员用户名: ')
    password = input('请输入管理员密码: ')
    
    if User.query.filter_by(username=username).first():
        print(f'用户 {username} 已存在！')
        return
    
    admin = User(username=username)
    admin.set_password(password)
    db.session.add(admin)
    db.session.commit()
    print(f'管理员用户 {username} 创建成功！')

@app.cli.command()
def create_user():
    """创建普通用户"""
    username = input('请输入用户名: ')
    password = input('请输入密码: ')
    
    if User.query.filter_by(username=username).first():
        print(f'用户 {username} 已存在！')
        return
    
    user = User(username=username)
    user.set_password(password)
    db.session.add(user)
    db.session.commit()
    print(f'用户 {username} 创建成功！')

if __name__ == '__main__':
    with app.app_context():
        # 确保数据库表存在
        db.create_all()
        
        # 检查是否有用户，如果没有则创建默认用户
        if User.query.count() == 0:
            print("检测到没有用户，创建默认用户...")
            default_user = User(username='admin')
            default_user.set_password('admin123')
            db.session.add(default_user)
            db.session.commit()
            print("默认用户创建完成: admin / admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5001)
